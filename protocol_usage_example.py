#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口协议使用示例教程
作者: <PERSON> (工程师)
用途: 演示如何正确使用SerialProtocol类
"""

from serial_protocol import SerialProtocol
import struct

def basic_usage_example():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 1. 创建协议实例
    protocol = SerialProtocol()
    
    # 2. 准备要发送的数据
    message = "Hello MaixCam!"
    data_to_send = message.encode('utf-8')  # 转换为字节
    
    # 3. 编码数据（打包）
    encoded_data = protocol.encode(data_to_send)
    print(f"原始数据: {message}")
    print(f"编码后数据: {encoded_data.hex()}")
    print(f"数据长度: {len(encoded_data)} 字节")
    
    # 4. 验证数据完整性
    is_valid, redundant_bytes = protocol.is_valid(encoded_data)
    print(f"数据验证结果: {is_valid} (0表示有效)")
    
    # 5. 解码数据（拆包）
    if is_valid == 0:
        decoded_data = protocol.decode(encoded_data)
        decoded_message = decoded_data.decode('utf-8')
        print(f"解码后数据: {decoded_message}")
    
    print()

def coordinate_data_example():
    """坐标数据传输示例（模拟人脸检测结果）"""
    print("=== 坐标数据传输示例 ===")
    
    protocol = SerialProtocol()
    
    # 模拟人脸检测结果：x, y, width, height
    face_coords = {
        'x': 120,
        'y': 80, 
        'width': 150,
        'height': 180
    }
    
    # 打包坐标数据（4个32位整数）
    coord_bytes = struct.pack('<iiii', 
                             face_coords['x'], 
                             face_coords['y'],
                             face_coords['width'], 
                             face_coords['height'])
    
    print(f"原始坐标: {face_coords}")
    print(f"打包后字节: {coord_bytes.hex()}")
    
    # 使用协议编码
    encoded_coords = protocol.encode(coord_bytes)
    print(f"协议编码后: {encoded_coords.hex()}")
    
    # 解码并解析
    if protocol.is_valid(encoded_coords)[0] == 0:
        decoded_coords = protocol.decode(encoded_coords)
        x, y, w, h = struct.unpack('<iiii', decoded_coords)
        print(f"解码后坐标: x={x}, y={y}, w={w}, h={h}")
    
    print()

def error_handling_example():
    """错误处理示例"""
    print("=== 错误处理示例 ===")
    
    protocol = SerialProtocol()
    
    # 测试各种错误情况
    test_cases = [
        (b'', "空数据"),
        (b'\xAA\x05\x00hello', "缺少校验和帧尾"),
        (b'\xBB\x05\x00hello\x00\x55', "错误的帧头"),
        (b'\xAA\x05\x00hello\x00\x66', "错误的帧尾"),
        (b'\xAA\x05\x00hell\x00\x55', "数据长度不匹配"),
    ]
    
    for test_data, description in test_cases:
        result, redundant = protocol.is_valid(test_data)
        print(f"{description}: 验证结果={result}")
        if result == -1:
            print("  → 数据太短")
        elif result == -2:
            print("  → 数据不完整")
        elif result == -3:
            print("  → 校验失败或格式错误")
    
    print()

def simulate_noisy_transmission():
    """模拟有噪声的传输环境"""
    print("=== 噪声传输模拟 ===")
    
    protocol = SerialProtocol()
    
    # 原始数据
    original_data = "测试数据".encode('utf-8')
    encoded = protocol.encode(original_data)
    
    # 模拟传输中的噪声（在前面添加垃圾数据）
    noise = b'\x12\x34\x56\x78'
    noisy_data = noise + encoded
    
    print(f"原始编码数据: {encoded.hex()}")
    print(f"带噪声数据: {noisy_data.hex()}")
    
    # 处理噪声数据
    is_valid, redundant_bytes = protocol.is_valid(noisy_data)
    print(f"冗余字节数: {redundant_bytes}")
    
    if is_valid == 0:
        # 去除冗余字节
        clean_data = noisy_data[redundant_bytes:]
        decoded = protocol.decode(clean_data)
        print(f"清理后解码: {decoded.decode('utf-8')}")
    
    print()

def performance_test():
    """性能测试"""
    print("=== 性能测试 ===")
    
    import time
    protocol = SerialProtocol()
    
    # 测试不同大小的数据
    test_sizes = [10, 100, 1000, 10000]
    
    for size in test_sizes:
        test_data = b'A' * size  # 创建指定大小的测试数据
        
        # 测试编码性能
        start_time = time.time()
        for _ in range(1000):  # 重复1000次
            encoded = protocol.encode(test_data)
        encode_time = time.time() - start_time
        
        # 测试解码性能
        start_time = time.time()
        for _ in range(1000):
            decoded = protocol.decode(encoded)
        decode_time = time.time() - start_time
        
        print(f"数据大小: {size} 字节")
        print(f"  编码时间: {encode_time:.4f}秒 (1000次)")
        print(f"  解码时间: {decode_time:.4f}秒 (1000次)")
    
    print()

if __name__ == "__main__":
    print("🚀 串口协议使用教程")
    print("=" * 50)
    
    try:
        basic_usage_example()
        coordinate_data_example()
        error_handling_example()
        simulate_noisy_transmission()
        performance_test()
        
        print("✅ 所有示例运行完成！")
        
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()
