# 🎯 小白也能看懂的串口协议文档

## 📚 目录
1. [这是什么？](#这是什么)
2. [为什么需要它？](#为什么需要它)
3. [怎么工作的？](#怎么工作的)
4. [代码详解](#代码详解)
5. [实际使用](#实际使用)
6. [常见问题](#常见问题)

---

## 🤔 这是什么？

想象一下，你要给朋友寄一个包裹：
- 📦 **包裹** = 你要传输的数据
- 🏷️ **标签** = 协议格式
- 📮 **邮局** = 串口通信

`protocol_test.py` 就是一个**智能邮局系统**，它能：
- 🎥 用摄像头检测人脸
- 📊 把人脸位置信息打包
- 📡 通过串口发送给其他设备
- ✅ 确保数据安全送达

---

## 🎯 为什么需要它？

### 问题场景
假设你有两台设备要互相传数据：
```
设备A (MaixCam) ←→ 设备B (电脑/单片机)
```

**没有协议的问题：**
- 😵 数据可能丢失
- 🔀 数据可能乱序
- ❓ 不知道数据从哪开始，哪结束
- 💥 一个错误就全乱了

**有了协议的好处：**
- ✅ 数据有固定格式
- 🛡️ 有错误检测机制
- 📏 知道数据长度
- 🔄 可以重传错误数据

---

## ⚙️ 怎么工作的？

### 数据包格式（就像快递包装）
```
┌─────┬──────┬────────┬──────┬─────┐
│开始 │ 长度 │ 实际数据 │ 校验 │ 结束│
│0xAA │2字节 │  N字节  │1字节 │0x55 │
└─────┴──────┴────────┴──────┴─────┘
```

**举个例子：**
要发送 "Hi" 这两个字符：
```
原始数据: "Hi"
打包后: [0xAA][2,0]["H","i"][校验码][0x55]
```

### 工作流程
```
1. 📷 摄像头拍照
2. 🔍 AI检测人脸
3. 📐 获取人脸坐标 (x,y,宽,高)
4. 📦 把坐标打包成数据包
5. 📡 通过串口发送
6. 👂 等待对方回复
7. 📖 解析回复数据
8. 🔄 重复循环
```

---

## 💻 代码详解

### 第一部分：导入和初始化
```python
from maix import camera, display, image, nn, app, uart
import struct
```
**解释：** 就像准备工具箱，导入需要的工具

```python
detector = nn.Retinaface(model="/root/models/retinaface.mud")
```
**解释：** 加载人脸检测AI模型，就像请了个专业摄影师

```python
cam = camera.Camera(...)
dis = display.Display()
```
**解释：** 启动摄像头和显示屏

```python
comm_proto = SerialProtocol()
serial = uart.UART("/dev/ttyS0", 115200)
```
**解释：** 
- 创建协议处理器（邮局工作人员）
- 打开串口（邮递通道），115200是传输速度

### 第二部分：主循环
```python
while not app.need_exit():
    img = cam.read()  # 📷 拍一张照片
    objs = detector.detect(img, conf_th = 0.4, iou_th = 0.45)  # 🔍 找人脸
```
**解释：** 
- 不停地拍照和找人脸
- `conf_th = 0.4` 表示至少40%确信是人脸才算
- `iou_th = 0.45` 是去重参数，避免同一张脸被识别多次

### 第三部分：处理检测结果
```python
for obj in objs:  # 对每个检测到的人脸
    img.draw_rect(obj.x, obj.y, obj.w, obj.h, color = image.COLOR_RED)  # 画红框
    payload = struct.pack('<iiii',obj.x,obj.y,obj.w,obj.h)  # 打包坐标
```
**解释：**
- 在人脸周围画红色方框
- 把坐标信息打包成二进制数据
- `<iiii` 表示4个32位整数，小端格式

### 第四部分：发送数据
```python
encoded = comm_proto.encode(payload)  # 📦 用协议打包
serial.write(encoded)  # 📡 发送出去
```
**解释：** 
- 用我们的协议把数据包装好
- 通过串口发送给其他设备

### 第五部分：接收回复
```python
length = serial.available()  # 检查有没有回复
if length > 0:
    data = serial.read(length)  # 读取数据
    data_buffer += data  # 添加到缓冲区
```
**解释：** 
- 看看对方有没有回信
- 把收到的数据存起来

### 第六部分：解析回复
```python
rc, bytes_redundant = comm_proto.is_valid(data_buffer)  # 检查数据完整性
if rc >= 0:  # 数据有效
    result = comm_proto.decode(data_buffer)  # 解包数据
    if len(result) == 16:  # 4个整数 = 16字节
        x,y,w,h = struct.unpack('<iiii', result)  # 解析坐标
        print('{},{},{},{}'.format(x,y,w,h))  # 打印结果
```
**解释：**
- 检查收到的包裹是否完好
- 如果完好就拆包
- 解析出坐标信息并打印

---

## 🚀 实际使用

### 运行前准备
1. **确保文件存在：**
   - `protocol_test.py` (主程序)
   - `serial_protocol.py` (协议类)

2. **检查硬件连接：**
   - 摄像头已连接
   - 串口线已连接到目标设备

3. **检查模型文件：**
   - 确保 `/root/models/retinaface.mud` 存在

### 运行步骤
```bash
# 在MaixVision中运行
python protocol_test.py
```

### 预期效果
- 屏幕显示摄像头画面
- 检测到人脸时画红框
- 串口发送人脸坐标数据
- 控制台打印接收到的回复数据

---

## ❓ 常见问题

### Q1: 为什么会出现 "AttributeError" 错误？
**A:** 导入问题，已修复：
```python
# 错误写法
import serial_protocol
comm_proto = serial_protocol.SerialProtocol()  # ❌

# 正确写法  
from serial_protocol import SerialProtocol
comm_proto = SerialProtocol()  # ✅
```

### Q2: 串口没有数据怎么办？
**A:** 检查：
- 串口设备路径是否正确 (`/dev/ttyS0`)
- 波特率是否匹配 (115200)
- 硬件连接是否正常
- 对方设备是否在监听

### Q3: 检测不到人脸怎么办？
**A:** 调整参数：
```python
# 降低检测阈值，更容易检测到人脸
objs = detector.detect(img, conf_th = 0.3, iou_th = 0.4)
```

### Q4: 数据传输错误怎么办？
**A:** 协议会自动处理：
- 自动寻找有效数据包
- 丢弃损坏的数据
- 通过校验和验证数据完整性

### Q5: 如何调试？
**A:** 添加调试信息：
```python
print(f"检测到 {len(objs)} 个人脸")
print(f"发送数据: {encoded.hex()}")
print(f"接收数据: {data.hex()}")
```

---

## 🎉 总结

这个程序就像一个**智能监控系统**：
1. 👁️ 眼睛（摄像头）看世界
2. 🧠 大脑（AI）识别人脸  
3. 📞 嘴巴（串口）告诉别人
4. 👂 耳朵（串口）听回复
5. 🔄 不停重复这个过程

**核心价值：** 让两个设备能够可靠地交换人脸检测信息！

---

## 🛠️ 动手实践

### 简单测试代码
创建一个测试文件 `simple_test.py`：
```python
from serial_protocol import SerialProtocol

# 创建协议实例
protocol = SerialProtocol()

# 测试发送 "Hello"
message = "Hello"
data = message.encode()  # 转换为字节
encoded = protocol.encode(data)  # 打包
print(f"原始: {message}")
print(f"打包后: {encoded.hex()}")

# 测试解包
decoded = protocol.decode(encoded)
result = decoded.decode()
print(f"解包后: {result}")
```

### 修改建议
如果你想自定义，可以修改这些地方：
```python
# 修改检测精度
conf_th = 0.4  # 改成 0.3 更容易检测，0.5 更严格

# 修改串口设备
device = "/dev/ttyS0"  # 可能需要改成 "/dev/ttyS1" 等

# 修改波特率
serial = uart.UART(device, 115200)  # 可以改成 9600, 38400 等
```

---

## 📖 扩展学习

### 相关概念
- **串口通信**: 设备间的数据传输方式
- **协议**: 数据传输的规则和格式
- **人脸检测**: AI识别图像中人脸的技术
- **二进制数据**: 计算机内部的数据格式

### 进阶方向
1. **学习更多AI模型**: 物体检测、图像分类
2. **学习网络通信**: TCP/IP, HTTP协议
3. **学习嵌入式开发**: Arduino, 树莓派编程
4. **学习图像处理**: OpenCV库的使用

---

## 🆘 求助指南

### 遇到问题时的步骤
1. **看错误信息**: 仔细阅读报错内容
2. **检查硬件**: 确认设备连接正常
3. **查看文档**: 重新阅读相关部分
4. **搜索资料**: 在网上搜索类似问题
5. **寻求帮助**: 向有经验的人请教

### 有用的调试技巧
```python
# 添加更多打印信息
print("程序开始运行...")
print(f"摄像头尺寸: {cam.width()}x{cam.height()}")
print(f"串口设备: {device}")

# 在关键位置添加断点
import time
time.sleep(1)  # 暂停1秒，方便观察
```

---

*📝 文档作者：Mike团队 | 适合人群：编程小白 | 难度：⭐⭐☆☆☆*
*🔄 最后更新：2025年1月 | 版本：v1.0*
